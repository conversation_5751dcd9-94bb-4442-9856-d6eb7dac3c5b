version: "3.5"

networks:
  fansbet-sweep:
    name: fansbet-sweep

services:
  user-frontend:
    build:
      context: ../player-end/frontend
      target: builder
    command: ["pnpm", "run", "dev"]
    volumes:
      - /home/<USER>/app/node_modules/
      - ../player-end/frontend:/home/<USER>/app/
    env_file: .env
    ports:
      - "6005:8080"
      - "4633:9229"
    depends_on:
      - user-backend
    logging:
      options:
        max-size: "200k"
        max-file: "10"
    networks:
      - fansbet-sweep

  nginx:
    image: nginx:latest
    restart: unless-stopped
    env_file: .env
    ports:
      - 61:80
      - 6443:443

    volumes:
      - ./nginx.conf:/etc/nginx/conf.d
    networks:
      - fansbet-sweep

  redis-database:
    image: redis:5.0.7-alpine
    volumes:
      - ./docker_volumes_data/redis-database:/data
    env_file: .env
    ports:
      - "6000:6379"
    networks:
      - fansbet-sweep

  database:
    image: postgres:12.1
    volumes:
      - ./docker_volumes_data/database:/var/lib/postgresql/data
    env_file: .env
    ports:
      - "6001:5432"
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    logging:
      options:
        max-size: "200k"
        max-file: "10"
    networks:
      - fansbet-sweep

  admin-backend:
    build:
      context: ../admin-end/backend
      target: builder
    command: ["npm", "run", "start:dev"]
    stdin_open: true
    tty: true
    volumes:
      - /home/<USER>/app/node_modules/
      - ../admin-end/backend:/home/<USER>/app/
    env_file: .env
    ports:
      - "6002:8080"
      - "4630:9229"
    depends_on:
      - database
    logging:
      options:
        max-size: "200k"
        max-file: "10"
    networks:
       - fansbet-sweep

  admin-frontend:
    build:
      context: ../admin-end/frontend
      target: builder
    command: ["npm", "run", "start"]
    stdin_open: true
    tty: true
    volumes:
      - /home/<USER>/app/node_modules/
      - ../admin-end/frontend:/home/<USER>/app/
    env_file: .env
    ports:
      - "6003:8080"
      - "4631:9229"
    depends_on:
      - database
    logging:
      options:
        max-size: "200k"
        max-file: "10"
    networks:
      - fansbet-sweep

  user-backend:
    build:
      context: ../player-end/backend
      target: builder
    command: ["npm", "run", "start:dev"]
    volumes:
      - /home/<USER>/app/node_modules/
      - ../player-end/backend:/home/<USER>/app/
    env_file: .env
    ports:
      - "6004:8080"
      - "4632:9229"
    depends_on:
      - database
    logging:
      options:
        max-size: "200k"
        max-file: "10"
    networks:
      - fansbet-sweep
  
  queue-scheduler:
    build:
      context: ../cron-scheduler
      target: builder
    command: ["npm", "run", "start:dev"]
    volumes:
      - /home/<USER>/app/node_modules/
      - ../cron-scheduler:/home/<USER>/app/
    ports:
      - "6007:8080"
      - "4635:9229"
    env_file: ../cron-scheduler/.env
    depends_on:
      - database
    logging:
      options:
        max-size: "200k"
        max-file: "10"
    networks:
      - fansbet-sweep

  queue-worker:
    build:
      context: ../cron-scheduler
      target: builder
    command: ["npm", "run", "start:dev:workers"]
    volumes:
      - /home/<USER>/app/node_modules/
      - ../cron-scheduler:/home/<USER>/app/
    ports:
      - "6008:8080"
      - "4636:9229"
    env_file: ../cron-scheduler/.env
    depends_on:
      - database
    logging:
      options:
        max-size: "200k"
        max-file: "10"
    networks:
      - fansbet-sweep

  pg-admin:
    image: dpage/pgadmin4:4.18
    #restart: always
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: postgres
      PGADMIN_LISTEN_PORT: 80
    env_file: .env
    ports:
      - "6009:80"
    volumes:
      - ./docker_volumes_data/pgadmin-data:/var/lib/pgadmin
    logging:
      options:
        max-size: "200k"
        max-file: "10"
    networks:
      - fansbet-sweep

  redis-dashboard:
    image: rediscommander/redis-commander:latest
    restart: always
    environment:
      - REDIS_HOSTS=redis-database
    env_file: .env
    ports:
      - "6010:8081"
    networks:
      - fansbet-sweep